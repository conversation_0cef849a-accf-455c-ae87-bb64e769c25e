
color code: #08387F
👥 User Roles:
    1. Student
        ◦ Login (With Matricule and password)
        ◦ Password recovery through email or phone number (This will be gotten from the information in the registration form)
        ◦ During the registration process, when the student submits the form, a verification code will be sent either to his phone number or to the email address, depending on the medium the student chooses.
        ◦ File new complaints
        ◦ Track complaint status (Unresolved, Processing, Resolve, Rejected)
        ◦ Receive updates (through email with link to uba website to login and view updated transcript)
        ◦ Give feedback
    2. Admin (School management)
        ◦ Manage user accounts (Block student accounts, Create departments and department accounts)
        ◦ Login (With Email/Username and Password)
        ◦ Set Datelines for submissions
        ◦ View all complaints, details and status
        ◦ Monitor resolution process
        ◦ Generate reports 
    3. Department Officer
        ◦ View complaints submitted to the respective department.(This is chosen on the complaint form and then when the student submits the complaint, it automatically goes to the department chosen on the form)
        ◦ Login (With Email/Username and Password)
        ◦ System sends a message to the Department officer’s email, at a particular time of the day let’s say (5:00pm), about the number of complaints which have been submitted for that day
        ◦ Respond and update status
        ◦ Communicate with students

🧩 Key Modules & Features:
1. User Authentication & Access Control
    • Login/Logout/Register
    • Role-based dashboard
2. Complaint Submission
    • Complaint category (e.g. CA, Exam, etc)
    • Description field
    • File attachments (optional)
    • Submission timestamp
3. Complaint Tracking & Status
    • Status: Pending, In Progress, Resolved, Rejected
    • Automatic updates via dashboard/email
4. Admin Panel
    • View and filter all complaints
    • Assign complaints to appropriate departments
    • Escalate unresolved complaints
5. Department/Staff Interface
    • View assigned complaints
    • Respond with resolution notes
    • Change complaint status
6. Notifications System
    • Email/SMS/On-site alerts for updates
    • New complaint alerts to admin/staff
7. Feedback & Satisfaction
    • Student rates resolution quality
    • Optional comment for feedback
8. Reports & Analytics
    • Monthly/quarterly/yearly reports
    • Complaint resolution time
    • Staff performance metrics

🛠️ Technologies You Can Use:
Layer
Suggested Tech Stack
Frontend: HTML, tailwind CSS,React
Backend:Python (Django/Flask)
Database: PostgreSQL
Authentication: JWT

COMPLAINT FORM
Name of Student:……………………………………………………….	
Registration Number:……………………………...	Year of Study:……………………
Department:………………………………… Date:……………………
Phone Number:…………………………………………………….
Academic Year:…………………………………… Semester Concerned:………………... 
Course Code:……………………………………………… Course Level:………………...  
Course Title:…………………………………………………………..
Complaint Concerning 	[checkbox]CA Mark 	[checkbox]Exam Mark     [checkbox]Other 
Description: …………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………………….



OTHER FEATURES
-Student should have profile page, where his/her information can be viewed. You can achieve this by making the student email icon above on the tabs of the student dashboard clickable (opens up the profile page)



Things are now better and I'm able to access the student dashboards. But the first error I want us to debug is the error I get when I open the complaints tab, I get the error:
Console Error


Error: Failed to fetch complaints

src/app/complaints/page.tsx (98:15) @ fetchComplaints


   96 |
   97 |       if (!response.ok) {
>  98 |         throw new Error('Failed to fetch complaints')
      |               ^
   99 |       }
  100 |
  101 |       const data = await response.json()
Call Stack
1

fetchComplaints
src/app/complaints/page.tsx (98:15)


Now the first error is, look likes I'm now able to login through the admin login page but I get the below error when the dashboard wants to load:
Error: supabaseKey is required.

src/lib/supabase-admin.ts (7:41) @ [project]/src/lib/supabase-admin.ts [app-client] (ecmascript)


   5 |
   6 | // Server-side Supabase client (for API routes only)
>  7 | export const supabaseAdmin = createClient(
     |                                         ^
   8 |   supabaseUrl,
   9 |   supabaseServiceKey,
  10 |   {
Call Stack
26

Show 21 ignore-listed frame(s)
new SupabaseClient
file:///home/<USER>/Desktop/complaint_sys/nahpi-complains/.next/static/chunks/node_modules_f9569b0d._.js (10514:33)
createClient
file:///home/<USER>/Desktop/complaint_sys/nahpi-complains/.next/static/chunks/node_modules_f9569b0d._.js (10719:12)
[project]/src/lib/supabase-admin.ts [app-client] (ecmascript)
src/lib/supabase-admin.ts (7:41)
[project]/src/lib/auth.ts [app-client] (ecmascript)
src/lib/auth.ts (2:1)
[project]/src/app/admin/dashboard/page.tsx [app-client] (ecmascript)
src/app/admin/dashboard/page.tsx (9:1)

For the second error I want you to debug, it occurs I even try to load the department login page:
Build Error


Ecmascript file had an error

./src/app/department/dashboard/page.tsx (167:10)

Ecmascript file had an error
  165 | }
  166 |
> 167 | function formatDate(date: Date) {
      |          ^^^^^^^^^^
  168 |   return date.toLocaleDateString('en-US', {
  169 |     year: 'numeric',
  170 |     month: 'short',

the name `formatDate` is defined multiple times
1
2
This error occurred during the build process and can only be dismissed by fixing the error.