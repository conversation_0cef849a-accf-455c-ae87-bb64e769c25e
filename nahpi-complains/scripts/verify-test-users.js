#!/usr/bin/env node

/**
 * NAHPi Complaints - Test Users Verification Script
 * 
 * This script verifies that all test users were created correctly
 * and can authenticate properly.
 * 
 * Usage: node scripts/verify-test-users.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase with service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Test users credentials
const testCredentials = [
  {
    email: '<EMAIL>',
    password: 'password123',
    expectedRole: 'student',
    expectedMatricule: 'UBa25E1000'
  },
  {
    email: '<EMAIL>',
    password: 'admin123',
    expectedRole: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'officer123',
    expectedRole: 'department_officer'
  }
];

async function verifyUser(credentials) {
  console.log(`\n🔍 Verifying user: ${credentials.email}`);
  
  try {
    // Test authentication
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password
    });

    if (authError) {
      console.error(`   ❌ Authentication failed: ${authError.message}`);
      return false;
    }

    console.log(`   ✅ Authentication successful`);
    const userId = authData.user.id;

    // Get user profile
    const { data: userProfile, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error(`   ❌ Failed to get user profile: ${userError.message}`);
      return false;
    }

    if (userProfile.role !== credentials.expectedRole) {
      console.error(`   ❌ Role mismatch: expected ${credentials.expectedRole}, got ${userProfile.role}`);
      return false;
    }

    console.log(`   ✅ User profile found with correct role: ${userProfile.role}`);

    // Role-specific verification
    if (credentials.expectedRole === 'student') {
      const { data: studentProfile, error: studentError } = await supabase
        .from('students')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (studentError) {
        console.error(`   ❌ Failed to get student profile: ${studentError.message}`);
        return false;
      }

      if (studentProfile.matricule !== credentials.expectedMatricule) {
        console.error(`   ❌ Matricule mismatch: expected ${credentials.expectedMatricule}, got ${studentProfile.matricule}`);
        return false;
      }

      console.log(`   ✅ Student profile found with correct matricule: ${studentProfile.matricule}`);

    } else if (credentials.expectedRole === 'department_officer') {
      const { data: officerProfile, error: officerError } = await supabase
        .from('department_officers')
        .select(`
          *,
          department:departments(name, code)
        `)
        .eq('user_id', userId)
        .single();

      if (officerError) {
        console.error(`   ❌ Failed to get department officer profile: ${officerError.message}`);
        return false;
      }

      console.log(`   ✅ Department officer profile found for department: ${officerProfile.department.name} (${officerProfile.department.code})`);
    }

    // Sign out
    await supabase.auth.signOut();
    console.log(`   ✅ User verification completed successfully`);
    return true;

  } catch (error) {
    console.error(`   ❌ Verification error: ${error.message}`);
    return false;
  }
}

async function verifyComplaint() {
  console.log(`\n📋 Verifying sample complaint...`);
  
  try {
    const { data: complaint, error } = await supabase
      .from('complaints')
      .select(`
        *,
        student:students(
          matricule,
          user:users(name, email)
        ),
        assigned_officer:department_officers(
          position,
          user:users(name, email)
        ),
        department:departments(name, code)
      `)
      .eq('complaint_id', 'CMP-2025-001')
      .single();

    if (error) {
      console.error(`   ❌ Failed to get sample complaint: ${error.message}`);
      return false;
    }

    console.log(`   ✅ Sample complaint found:`);
    console.log(`      - ID: ${complaint.complaint_id}`);
    console.log(`      - Title: ${complaint.title}`);
    console.log(`      - Student: ${complaint.student.user.name} (${complaint.student.matricule})`);
    console.log(`      - Assigned to: ${complaint.assigned_officer.user.name} (${complaint.assigned_officer.position})`);
    console.log(`      - Department: ${complaint.department.name} (${complaint.department.code})`);
    console.log(`      - Status: ${complaint.status}`);

    return true;

  } catch (error) {
    console.error(`   ❌ Complaint verification error: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🔍 Starting NAHPi Complaints test users verification...\n');
  
  let allPassed = true;

  // Verify each test user
  for (const credentials of testCredentials) {
    const passed = await verifyUser(credentials);
    if (!passed) {
      allPassed = false;
    }
  }

  // Verify sample complaint
  const complaintPassed = await verifyComplaint();
  if (!complaintPassed) {
    allPassed = false;
  }

  console.log('\n' + '='.repeat(60));
  
  if (allPassed) {
    console.log('🎉 All verifications passed! Test users are ready for use.');
    console.log('\n📋 Test Login Credentials:');
    console.log('   👨‍🎓 Student: <EMAIL> / password123 (Matricule: UBa25E1000)');
    console.log('   👨‍💼 Admin: <EMAIL> / admin123');
    console.log('   👨‍🏫 Department Officer: <EMAIL> / officer123');
    console.log('\n🌐 Test the application at: http://localhost:3000');
  } else {
    console.log('❌ Some verifications failed. Please check the errors above.');
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
