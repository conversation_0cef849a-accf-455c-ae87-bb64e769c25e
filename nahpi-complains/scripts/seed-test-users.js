#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON><PERSON> Complaints - Test Users Seeding Script
 * 
 * This script creates default test users for development and testing.
 * It uses the Supabase Admin API to create users and their associated profiles.
 * 
 * Usage: node scripts/seed-test-users.js
 * 
 * Make sure your .env.local file has the correct Supabase credentials:
 * - NEXT_PUBLIC_SUPABASE_URL
 * - SUPABASE_SERVICE_ROLE_KEY (Admin key, not anon key)
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase with service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - S<PERSON><PERSON>ASE_SERVICE_ROLE_KEY');
  console.error('\nMake sure your .env.local file is properly configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Test users data
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Student',
    role: 'student',
    profile: {
      matricule: 'UBa25E1000',
      department: 'Computer Science',
      year_of_study: 3,
      phone_number: '+237123456789',
      academic_year: '2024-2025',
      verification_method: 'email',
      is_verified: true
    }
  },
  {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Test Administrator',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'officer123',
    name: 'Dr. Test Officer',
    role: 'department_officer',
    profile: {
      position: 'Assistant Professor',
      department_code: 'CS' // Will be resolved to department_id
    }
  }
];

async function getDepartmentId(code) {
  const { data, error } = await supabase
    .from('departments')
    .select('id')
    .eq('code', code)
    .single();
  
  if (error) {
    console.error(`❌ Error getting department ${code}:`, error.message);
    return null;
  }
  
  return data.id;
}

async function createTestUser(userData) {
  console.log(`\n📝 Creating user: ${userData.email}`);
  
  try {
    // 1. Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        name: userData.name,
        role: userData.role
      }
    });

    if (authError) {
      if (authError.message.includes('already registered')) {
        console.log(`   ⚠️  User ${userData.email} already exists, skipping...`);
        return;
      }
      throw authError;
    }

    const userId = authData.user.id;
    console.log(`   ✅ Auth user created with ID: ${userId}`);

    // 2. Create user profile in public.users table
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        is_active: true
      });

    if (userError) {
      console.error(`   ❌ Error creating user profile:`, userError.message);
      return;
    }

    console.log(`   ✅ User profile created`);

    // 3. Create role-specific profile
    if (userData.role === 'student') {
      const { error: studentError } = await supabase
        .from('students')
        .insert({
          user_id: userId,
          matricule: userData.profile.matricule,
          department: userData.profile.department,
          year_of_study: userData.profile.year_of_study,
          phone_number: userData.profile.phone_number,
          academic_year: userData.profile.academic_year,
          verification_method: userData.profile.verification_method,
          is_verified: userData.profile.is_verified
        });

      if (studentError) {
        console.error(`   ❌ Error creating student profile:`, studentError.message);
        return;
      }

      console.log(`   ✅ Student profile created with matricule: ${userData.profile.matricule}`);

    } else if (userData.role === 'department_officer') {
      const departmentId = await getDepartmentId(userData.profile.department_code);
      
      if (!departmentId) {
        console.error(`   ❌ Department ${userData.profile.department_code} not found`);
        return;
      }

      const { error: officerError } = await supabase
        .from('department_officers')
        .insert({
          user_id: userId,
          department_id: departmentId,
          position: userData.profile.position
        });

      if (officerError) {
        console.error(`   ❌ Error creating department officer profile:`, officerError.message);
        return;
      }

      console.log(`   ✅ Department officer profile created for ${userData.profile.department_code} department`);
    }

    console.log(`   🎉 User ${userData.email} created successfully!`);

  } catch (error) {
    console.error(`   ❌ Error creating user ${userData.email}:`, error.message);
  }
}

async function createSampleComplaint() {
  console.log(`\n📋 Creating sample complaint...`);
  
  try {
    // Get the student ID
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('id')
      .eq('matricule', 'UBa25E1000')
      .single();

    if (studentError || !student) {
      console.error('   ❌ Test student not found, skipping complaint creation');
      return;
    }

    // Get the department officer ID
    const { data: officer, error: officerError } = await supabase
      .from('department_officers')
      .select('id')
      .eq('position', 'Assistant Professor')
      .single();

    if (officerError || !officer) {
      console.error('   ❌ Test department officer not found, skipping complaint creation');
      return;
    }

    // Get CS department ID
    const departmentId = await getDepartmentId('CS');
    if (!departmentId) {
      console.error('   ❌ CS department not found, skipping complaint creation');
      return;
    }

    // Create the complaint
    const { error: complaintError } = await supabase
      .from('complaints')
      .insert({
        complaint_id: 'CMP-2025-001',
        student_id: student.id,
        title: 'CA Mark Discrepancy in Data Structures',
        description: 'I believe there is an error in my CA mark calculation for the Data Structures course. My expected score based on assignments and tests should be higher than what was recorded.',
        category: 'ca_mark',
        status: 'pending',
        priority: 'medium',
        course_code: 'CS301',
        course_title: 'Data Structures and Algorithms',
        course_level: 'Level 3',
        semester: 'Semester 1',
        academic_year: '2024-2025',
        department_id: departmentId,
        assigned_to: officer.id
      });

    if (complaintError) {
      if (complaintError.message.includes('duplicate key')) {
        console.log('   ⚠️  Sample complaint already exists, skipping...');
        return;
      }
      console.error('   ❌ Error creating sample complaint:', complaintError.message);
      return;
    }

    console.log('   ✅ Sample complaint created: CMP-2025-001');

  } catch (error) {
    console.error('   ❌ Error creating sample complaint:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting NAHPi Complaints test users seeding...\n');
  
  // Check if departments exist
  const { data: departments, error: deptError } = await supabase
    .from('departments')
    .select('name, code')
    .limit(5);

  if (deptError || !departments || departments.length === 0) {
    console.error('❌ No departments found. Please run the main schema script first.');
    process.exit(1);
  }

  console.log('📚 Found departments:', departments.map(d => `${d.name} (${d.code})`).join(', '));

  // Create test users
  for (const userData of testUsers) {
    await createTestUser(userData);
  }

  // Create sample complaint
  await createSampleComplaint();

  console.log('\n🎉 Test users seeding completed!');
  console.log('\n📋 Test Login Credentials:');
  console.log('   👨‍🎓 Student: <EMAIL> / password123 (Matricule: UBa25E1000)');
  console.log('   👨‍💼 Admin: <EMAIL> / admin123');
  console.log('   👨‍🏫 Department Officer: <EMAIL> / officer123');
  console.log('\n💡 You can now test the application with these accounts!');
}

// Run the script
main().catch(console.error);
