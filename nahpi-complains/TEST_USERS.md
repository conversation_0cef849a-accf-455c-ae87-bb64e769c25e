# NAHPi Complaints - Test Users Documentation

This document provides information about the default test users created for development and testing purposes.

## 🚀 Quick Start

The test users have been automatically created and are ready to use. You can log in to the application at [http://localhost:3000](http://localhost:3000) using the credentials below.

## 👥 Test User Accounts

### 👨‍🎓 Test Student
- **Email:** `<EMAIL>`
- **Password:** `password123`
- **Matricule:** `UBa25E1000` (New format: UBa + year + letter + digits)
- **Department:** Computer Science
- **Year of Study:** 3rd Year
- **Phone:** +************
- **Academic Year:** 2024-2025
- **Status:** Verified

**Login URL:** [http://localhost:3000/login](http://localhost:3000/login)

### 👨‍💼 Test Administrator
- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** System Administrator
- **Permissions:** Full system access

**Login URL:** [http://localhost:3000/admin/login](http://localhost:3000/admin/login)

### 👨‍🏫 Test Department Officer
- **Email:** `<EMAIL>`
- **Password:** `officer123`
- **Department:** Computer Science (CS)
- **Position:** Assistant Professor
- **Role:** Department Officer

**Login URL:** [http://localhost:3000/department/login](http://localhost:3000/department/login)

## 📋 Sample Data

### Test Complaint
A sample complaint has been created for testing purposes:

- **Complaint ID:** CMP-2025-001
- **Title:** CA Mark Discrepancy in Data Structures
- **Student:** Test Student (UBa25E1000)
- **Course:** CS301 - Data Structures and Algorithms
- **Category:** CA Mark
- **Status:** Pending
- **Priority:** Medium
- **Assigned to:** Dr. Test Officer (CS Department)

## 🔧 Management Scripts

### Creating Test Users
```bash
# Run the seeding script to create test users
node scripts/seed-test-users.js
```

### Verifying Test Users
```bash
# Run the verification script to check all test users
node scripts/verify-test-users.js
```

## 🧪 Testing Scenarios

### Student Testing
1. **Login:** Use student credentials to access the student portal
2. **Dashboard:** View complaint statistics and recent activity
3. **Submit Complaint:** Create new complaints with the test student account
4. **Track Status:** Monitor complaint progress and responses
5. **Provide Feedback:** Rate resolved complaints

### Admin Testing
1. **Login:** Use admin credentials to access the admin panel
2. **User Management:** View and manage all system users
3. **Department Management:** Create and manage departments
4. **Complaint Oversight:** Monitor all complaints across departments
5. **System Settings:** Configure application settings

### Department Officer Testing
1. **Login:** Use department officer credentials to access the department portal
2. **Department Dashboard:** View CS department-specific statistics
3. **Complaint Management:** Handle complaints assigned to CS department
4. **Student Communication:** Respond to student complaints
5. **Performance Metrics:** Monitor department resolution rates

## 🔐 Security Notes

- **Development Only:** These test accounts are for development and testing purposes only
- **Default Passwords:** All test accounts use simple passwords for convenience
- **Production Warning:** Never use these accounts or passwords in production
- **Data Reset:** Test data can be reset by re-running the seeding scripts

## 🗂️ Database Structure

### User Roles
- **student:** Can submit and track complaints
- **admin:** Full system administration access
- **department_officer:** Department-specific complaint management

### Matricule Format
The new matricule format follows the pattern: `UBa<year><letter><digits>`
- Example: `UBa25E1000`
- `UBa`: University prefix
- `25`: Last two digits of year (2025)
- `E`: Any letter
- `1000`: Any four digits

## 🚨 Troubleshooting

### If Test Users Don't Exist
```bash
# Re-run the seeding script
node scripts/seed-test-users.js
```

### If Login Fails
1. Check that the development server is running: `npm run dev`
2. Verify environment variables in `.env.local`
3. Run the verification script: `node scripts/verify-test-users.js`

### If Database Issues Occur
1. Check Supabase connection in the dashboard
2. Verify RLS policies are properly configured
3. Ensure all required tables exist

## 📞 Support

If you encounter issues with the test users:

1. Check the console logs for error messages
2. Verify your Supabase configuration
3. Run the verification script to diagnose issues
4. Re-run the seeding script if needed

## 🔄 Resetting Test Data

To reset all test data:

1. Delete test users from Supabase Auth dashboard
2. Clear related records from database tables
3. Re-run the seeding script: `node scripts/seed-test-users.js`

---

**Last Updated:** January 2025  
**Version:** 1.0.0
