'use client'

import { supabase } from './supabase'

export interface AuthUser {
  id: string
  email: string
  role: 'student' | 'admin' | 'department_officer'
  matricule?: string
  department?: string
  name?: string
}

class ClientAuthService {
  // Get current user (client-side only)
  async getCurrentUser(): Promise<{ user: AuthUser | null; profile: any | null }> {
    try {
      console.log('🔍 [ClientAuthService] Getting current user...')

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('getUser timeout')), 5000)
      )

      const getUserPromise = supabase.auth.getUser()
      const { data: { user }, error } = await Promise.race([getUserPromise, timeoutPromise]) as any
      console.log('🔍 [ClientAuthService] Supabase getUser result:', { user: !!user, error: error?.message })

      if (error || !user) {
        console.log('🔍 [ClientAuthService] No user or error, returning null')
        return { user: null, profile: null }
      }

      // For client-side, we'll get user info from the session/user metadata
      // The server-side auth will handle the actual database queries
      console.log('🔍 [ClientAuthService] User metadata:', user.user_metadata)
      const authUser: AuthUser = {
        id: user.id,
        email: user.email!,
        role: (user.user_metadata?.role || 'student') as AuthUser['role'],
        matricule: user.user_metadata?.matricule,
        department: user.user_metadata?.department,
        name: user.user_metadata?.name
      }

      console.log('🔍 [ClientAuthService] Returning auth user:', authUser)
      return { user: authUser, profile: user.user_metadata }
    } catch (error) {
      console.error('Error getting current user:', error)
      return { user: null, profile: null }
    }
  }

  // Sign out (client-side)
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { data: null, error: null }
    } catch (error) {
      console.error('Error signing out:', error)
      return { data: null, error }
    }
  }

  // Reset password (client-side)
  async resetPassword(email: string) {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })
      return { data, error }
    } catch (error) {
      console.error('Error resetting password:', error)
      return { data: null, error }
    }
  }

  // Client-side authentication methods that call API endpoints
  async signInUser(email: string, password: string, role: 'admin' | 'department_officer') {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: email,
          password,
          role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        return { data: null, error: { message: result.error } }
      }

      // Set the session in the client-side Supabase instance
      if (result.session) {
        const { error: sessionError } = await supabase.auth.setSession({
          access_token: result.session.access_token,
          refresh_token: result.session.refresh_token
        })

        if (sessionError) {
          console.error('Error setting session:', sessionError)
          return { data: null, error: sessionError }
        }
      }

      return { data: result, error: null }
    } catch (error) {
      console.error('Error signing in user:', error)
      return { data: null, error }
    }
  }

  async signInStudent(matricule: string, password: string) {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: matricule,
          password,
          role: 'student'
        })
      })

      const result = await response.json()

      if (!response.ok) {
        return { data: null, error: { message: result.error } }
      }

      // Set the session in the client-side Supabase instance
      if (result.session) {
        const { error: sessionError } = await supabase.auth.setSession({
          access_token: result.session.access_token,
          refresh_token: result.session.refresh_token
        })

        if (sessionError) {
          console.error('Error setting session:', sessionError)
          return { data: null, error: sessionError }
        }
      }

      return { data: result, error: null }
    } catch (error) {
      console.error('Error signing in student:', error)
      return { data: null, error }
    }
  }

  async signUpStudent(data: any) {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()
      
      if (!response.ok) {
        return { data: null, error: { message: result.error } }
      }

      return { data: result, error: null }
    } catch (error) {
      console.error('Error signing up student:', error)
      return { data: null, error }
    }
  }
}

export const clientAuthService = new ClientAuthService()
