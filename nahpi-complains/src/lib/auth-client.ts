'use client'

import { supabase } from './supabase'

export interface AuthUser {
  id: string
  email: string
  role: 'student' | 'admin' | 'department_officer'
  matricule?: string
  department?: string
  name?: string
}

interface StoredAuthData {
  user: AuthUser
  session: {
    access_token: string
    refresh_token: string
    expires_at: number
  }
  timestamp: number
}

class ClientAuthService {
  private readonly STORAGE_KEY = 'nahpi_auth_data'
  private readonly STORAGE_EXPIRY = 24 * 60 * 60 * 1000 // 24 hours
  private authChangeListeners: (() => void)[] = []

  // Store authentication data in localStorage
  private storeAuthData(user: AuthUser, session: any): void {
    try {
      const authData: StoredAuthData = {
        user,
        session: {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at * 1000 // Convert to milliseconds
        },
        timestamp: Date.now()
      }
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(authData))
      console.log('🔍 [ClientAuthService] Auth data stored in localStorage')
    } catch (error) {
      console.error('🔍 [ClientAuthService] Error storing auth data:', error)
    }
  }

  // Get stored authentication data from localStorage
  private getStoredAuthData(): StoredAuthData | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) return null

      const authData: StoredAuthData = JSON.parse(stored)

      // Check if data is expired
      if (Date.now() - authData.timestamp > this.STORAGE_EXPIRY) {
        console.log('🔍 [ClientAuthService] Stored auth data expired, clearing...')
        this.clearStoredAuthData()
        return null
      }

      // Check if session token is expired
      if (Date.now() > authData.session.expires_at) {
        console.log('🔍 [ClientAuthService] Session token expired, clearing...')
        this.clearStoredAuthData()
        return null
      }

      return authData
    } catch (error) {
      console.error('🔍 [ClientAuthService] Error reading stored auth data:', error)
      return null
    }
  }

  // Clear stored authentication data
  private clearStoredAuthData(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      console.log('🔍 [ClientAuthService] Auth data cleared from localStorage')
    } catch (error) {
      console.error('🔍 [ClientAuthService] Error clearing auth data:', error)
    }
  }

  // Get current user using new approach
  async getCurrentUser(): Promise<{ user: AuthUser | null; profile: any | null }> {
    try {
      console.log('🔍 [ClientAuthService] Getting current user with new approach...')

      // First, try to get from localStorage
      const storedData = this.getStoredAuthData()
      if (storedData) {
        console.log('🔍 [ClientAuthService] Found valid stored auth data:', {
          user: storedData.user.email,
          role: storedData.user.role,
          timestamp: new Date(storedData.timestamp).toLocaleString(),
          sessionExpires: new Date(storedData.session.expires_at).toLocaleString()
        })
        return { user: storedData.user, profile: storedData.user }
      }

      // If no stored data, try to get from Supabase session (with timeout)
      console.log('🔍 [ClientAuthService] No stored data, checking Supabase session...')
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('getSession timeout')), 3000)
      )

      const getSessionPromise = supabase.auth.getSession()
      const { data: { session }, error } = await Promise.race([getSessionPromise, timeoutPromise]) as any

      if (error) {
        console.log('🔍 [ClientAuthService] Session check error:', error.message)
        return { user: null, profile: null }
      }

      if (!session?.user) {
        console.log('🔍 [ClientAuthService] No active session found')
        return { user: null, profile: null }
      }

      console.log('🔍 [ClientAuthService] Found Supabase session, extracting user data...')
      const user = session.user

      // Extract user data from session metadata
      console.log('🔍 [ClientAuthService] User metadata:', user.user_metadata)
      const authUser: AuthUser = {
        id: user.id,
        email: user.email!,
        role: (user.user_metadata?.role || 'student') as AuthUser['role'],
        matricule: user.user_metadata?.matricule,
        department: user.user_metadata?.department,
        name: user.user_metadata?.name
      }

      // Store the auth data for future use
      this.storeAuthData(authUser, session)

      console.log('🔍 [ClientAuthService] Returning auth user from session:', authUser)
      return { user: authUser, profile: user.user_metadata }
    } catch (error) {
      console.error('🔍 [ClientAuthService] Error getting current user:', error)
      return { user: null, profile: null }
    }
  }

  // Add auth change listener
  onAuthChange(listener: () => void): () => void {
    this.authChangeListeners.push(listener)
    return () => {
      const index = this.authChangeListeners.indexOf(listener)
      if (index > -1) {
        this.authChangeListeners.splice(index, 1)
      }
    }
  }

  // Notify all listeners of auth changes
  private notifyAuthChange(): void {
    console.log('🔍 [ClientAuthService] Notifying auth change listeners:', this.authChangeListeners.length)
    this.authChangeListeners.forEach(listener => {
      try {
        listener()
      } catch (error) {
        console.error('🔍 [ClientAuthService] Error in auth change listener:', error)
      }
    })
  }

  // Set user data after successful login
  setUserData(user: AuthUser, session: any): void {
    console.log('🔍 [ClientAuthService] Setting user data after login:', {
      user: user.email,
      role: user.role,
      sessionExpires: session.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'unknown'
    })
    this.storeAuthData(user, session)

    // Verify storage worked
    const stored = this.getStoredAuthData()
    if (stored) {
      console.log('🔍 [ClientAuthService] Verification: Data successfully stored and retrieved')
    } else {
      console.error('🔍 [ClientAuthService] Verification: Failed to store or retrieve data!')
    }

    // Notify listeners that auth data has changed
    this.notifyAuthChange()
  }

  // Sign out user
  async signOut(): Promise<void> {
    try {
      console.log('🔍 [ClientAuthService] Signing out user...')
      this.clearStoredAuthData()
      await supabase.auth.signOut()
      console.log('🔍 [ClientAuthService] User signed out successfully')
    } catch (error) {
      console.error('🔍 [ClientAuthService] Error signing out:', error)
    }
  }

  // Sign out (client-side)
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { data: null, error: null }
    } catch (error) {
      console.error('Error signing out:', error)
      return { data: null, error }
    }
  }

  // Reset password (client-side)
  async resetPassword(email: string) {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })
      return { data, error }
    } catch (error) {
      console.error('Error resetting password:', error)
      return { data: null, error }
    }
  }

  // Client-side authentication methods that call API endpoints
  async signInUser(email: string, password: string, role: 'admin' | 'department_officer') {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: email,
          password,
          role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        return { data: null, error: { message: result.error } }
      }

      // Set the session in the client-side Supabase instance
      if (result.session) {
        const { error: sessionError } = await supabase.auth.setSession({
          access_token: result.session.access_token,
          refresh_token: result.session.refresh_token
        })

        if (sessionError) {
          console.error('Error setting session:', sessionError)
          return { data: null, error: sessionError }
        }
      }

      return { data: result, error: null }
    } catch (error) {
      console.error('Error signing in user:', error)
      return { data: null, error }
    }
  }

  async signInStudent(matricule: string, password: string) {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: matricule,
          password,
          role: 'student'
        })
      })

      const result = await response.json()

      if (!response.ok) {
        return { data: null, error: { message: result.error } }
      }

      // Set the session in the client-side Supabase instance
      if (result.session) {
        const { error: sessionError } = await supabase.auth.setSession({
          access_token: result.session.access_token,
          refresh_token: result.session.refresh_token
        })

        if (sessionError) {
          console.error('Error setting session:', sessionError)
          return { data: null, error: sessionError }
        }
      }

      return { data: result, error: null }
    } catch (error) {
      console.error('Error signing in student:', error)
      return { data: null, error }
    }
  }

  async signUpStudent(data: any) {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()
      
      if (!response.ok) {
        return { data: null, error: { message: result.error } }
      }

      return { data: result, error: null }
    } catch (error) {
      console.error('Error signing up student:', error)
      return { data: null, error }
    }
  }
}

export const clientAuthService = new ClientAuthService()
