import { supabase } from './supabase'
import { NextRequest } from 'next/server'
import { User } from '@supabase/supabase-js'
import { createClient } from '@supabase/supabase-js'

// Create a server-side client for authentication
const supabaseAuth = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export interface AuthUser extends User {
  role?: 'student' | 'admin' | 'department_officer'
  name?: string
  is_active?: boolean
}

export interface StudentProfile {
  id: string
  user_id: string
  matricule: string
  department: string
  year_of_study: number
  phone_number: string
  academic_year: string
  verification_method: 'email' | 'phone'
  is_verified: boolean
}

export interface DepartmentOfficerProfile {
  id: string
  user_id: string
  department_id: string
  position: string
  department: {
    id: string
    name: string
    code: string
  }
}

// Authentication functions
export const authService = {
  // Sign up student
  async signUpStudent(data: {
    email: string
    password: string
    name: string
    matricule: string
    department: string
    year_of_study: number
    phone_number: string
    academic_year: string
    verification_method: 'email' | 'phone'
  }) {
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name,
            role: 'student'
          }
        }
      })

      if (authError) throw authError

      if (authData.user) {
        // Create user profile
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: data.email,
            name: data.name,
            role: 'student'
          })

        if (userError) throw userError

        // Create student profile
        const { error: studentError } = await supabase
          .from('students')
          .insert({
            user_id: authData.user.id,
            matricule: data.matricule,
            department: data.department,
            year_of_study: data.year_of_study,
            phone_number: data.phone_number,
            academic_year: data.academic_year,
            verification_method: data.verification_method
          })

        if (studentError) throw studentError

        // Send verification code
        await this.sendVerificationCode(authData.user.id, data.verification_method)
      }

      return { data: authData, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Sign in student with matricule (client-side)
  async signInStudent(matricule: string, password: string) {
    try {
      // Use API endpoint for server-side authentication
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: matricule,
          password,
          role: 'student'
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Sign in failed')
      }

      return { data: result, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Sign in admin/department officer (client-side)
  async signInUser(email: string, password: string, role: 'admin' | 'department_officer') {
    try {
      // Use API endpoint for server-side authentication
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: email,
          password,
          role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Sign in failed')
      }

      return { data: result, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Get current user with profile
  async getCurrentUser(): Promise<{ user: AuthUser | null; profile: any | null }> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        return { user: null, profile: null }
      }

      // Get user profile
      const { data: userProfile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        return { user, profile: null }
      }

      const authUser: AuthUser = {
        ...user,
        role: userProfile.role,
        name: userProfile.name,
        is_active: userProfile.is_active
      }

      // Get role-specific profile
      let profile = null
      if (userProfile.role === 'student') {
        const { data: studentProfile } = await supabase
          .from('students')
          .select('*')
          .eq('user_id', user.id)
          .single()
        profile = studentProfile
      } else if (userProfile.role === 'department_officer') {
        const { data: officerProfile } = await supabase
          .from('department_officers')
          .select(`
            *,
            department:departments(id, name, code)
          `)
          .eq('user_id', user.id)
          .single()
        profile = officerProfile
      }

      return { user: authUser, profile }
    } catch (error) {
      return { user: null, profile: null }
    }
  },



  // Send verification code
  async sendVerificationCode(userId: string, method: 'email' | 'phone') {
    const code = Math.floor(100000 + Math.random() * 900000).toString()
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Store verification code
    const { error } = await supabase
      .from('verification_codes')
      .insert({
        user_id: userId,
        code,
        type: method,
        expires_at: expiresAt.toISOString()
      })

    if (error) throw error

    // TODO: Send actual email/SMS
    console.log(`Verification code for ${method}: ${code}`)
    
    return { success: true }
  },

  // Verify code
  async verifyCode(userId: string, code: string) {
    const { data, error } = await supabase
      .from('verification_codes')
      .select('*')
      .eq('user_id', userId)
      .eq('code', code)
      .eq('is_used', false)
      .gte('expires_at', new Date().toISOString())
      .single()

    if (error || !data) {
      throw new Error('Invalid or expired verification code')
    }

    // Mark code as used
    await supabase
      .from('verification_codes')
      .update({ is_used: true })
      .eq('id', data.id)

    // Mark student as verified
    await supabase
      .from('students')
      .update({ is_verified: true })
      .eq('user_id', userId)

    return { success: true }
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/reset-password`
    })

    if (error) throw error
    return { success: true }
  }
}
