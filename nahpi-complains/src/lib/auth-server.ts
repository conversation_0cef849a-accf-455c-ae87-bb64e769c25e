import { supabaseAdmin } from './supabase-admin'
import { NextRequest } from 'next/server'
import { User, createClient } from '@supabase/supabase-js'

export interface AuthUser extends User {
  role?: 'student' | 'admin' | 'department_officer'
  name?: string
  is_active?: boolean
}

export interface UserProfile {
  id: string
  email: string
  name: string
  role: 'student' | 'admin' | 'department_officer'
  is_active: boolean
  created_at: string
  updated_at: string
}

// Create a regular Supabase client for authentication (server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey)

class ServerAuthService {
  /**
   * Get current user from request (server-side only)
   */
  async getCurrentUserFromRequest(request: NextRequest): Promise<{ user: AuthUser | null, profile: UserProfile | null }> {
    try {
      const authHeader = request.headers.get('authorization')
      console.log('🔍 [ServerAuth] Auth header:', authHeader ? `Bearer ${authHeader.substring(7, 20)}...` : 'missing')

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.log('🔍 [ServerAuth] No valid auth header')
        return { user: null, profile: null }
      }

      const token = authHeader.substring(7) // Remove 'Bearer ' prefix
      console.log('🔍 [ServerAuth] Token length:', token.length)

      // Verify the JWT token using Supabase admin client
      const { data: { user }, error } = await supabaseAdmin.auth.getUser(token)
      console.log('🔍 [ServerAuth] Token verification:', { user: !!user, error: error?.message })

      if (error || !user) {
        return { user: null, profile: null }
      }

      // Get user profile using admin client
      const { data: userProfile, error: profileError } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError || !userProfile) {
        console.error('Error fetching user profile:', profileError)
        return { user: null, profile: null }
      }

      // Check if user is active
      if (!userProfile.is_active) {
        return { user: null, profile: null }
      }

      // Skip role-specific profile fetching for performance
      // Use getCurrentUserWithFullProfile() when detailed profile is needed

      // Enhance user object with profile data
      const enhancedUser: AuthUser = {
        ...user,
        role: userProfile.role,
        name: userProfile.name,
        is_active: userProfile.is_active
      }

      return {
        user: enhancedUser,
        profile: userProfile
      }
    } catch (error) {
      console.error('Error getting current user from request:', error)
      return { user: null, profile: null }
    }
  }

  /**
   * Get current user with full profile from request (server-side only)
   */
  async getCurrentUserWithFullProfile(request: NextRequest): Promise<{ user: AuthUser | null, profile: any }> {
    try {
      const { user } = await this.getCurrentUserFromRequest(request)

      if (!user) {
        return { user: null, profile: null }
      }

      // Get role-specific profile only when needed
      let profile = null
      if (user.role === 'student') {
        const { data: studentProfile } = await supabaseAdmin
          .from('students')
          .select('*')
          .eq('user_id', user.id)
          .single()
        profile = studentProfile
      } else if (user.role === 'department_officer') {
        const { data: officerProfile } = await supabaseAdmin
          .from('department_officers')
          .select(`
            *,
            department:departments(id, name, code)
          `)
          .eq('user_id', user.id)
          .single()
        profile = officerProfile
      }

      return { user, profile }
    } catch (error) {
      console.error('Error getting current user with full profile:', error)
      return { user: null, profile: null }
    }
  }

  /**
   * Sign in student by matricule (server-side only)
   */
  async signInStudent(matricule: string, password: string) {
    try {
      // Get student email by matricule using admin client
      const { data: studentData, error: studentError } = await supabaseAdmin
        .from('students')
        .select('user_id, users!inner(email, is_active)')
        .eq('matricule', matricule)
        .single()

      if (studentError || !studentData) {
        return { user: null, session: null, error: 'Invalid matricule or password' }
      }

      if (!studentData.users.is_active) {
        return { user: null, session: null, error: 'Account is deactivated' }
      }

      const email = studentData.users.email

      // Sign in with email and password using regular auth client
      const { data, error } = await supabaseAuth.auth.signInWithPassword({
        email,
        password
      })

      if (error || !data.user) {
        return { user: null, session: null, error: error?.message }
      }

      // Update user metadata with role and profile information
      try {
        const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
          data.user.id,
          {
            user_metadata: {
              role: 'student',
              matricule: studentData.matricule,
              name: studentData.name || studentData.users.email,
              email: studentData.users.email,
              is_active: studentData.is_active,
              department: studentData.department_name
            }
          }
        )

        if (updateError) {
          console.error('Error updating student user metadata:', updateError)
        }
      } catch (metadataError) {
        console.error('Error setting student user metadata:', metadataError)
      }

      return { user: data.user, session: data.session, error: error?.message }
    } catch (error) {
      console.error('Error signing in student:', error)
      return { user: null, session: null, error: 'Sign in failed' }
    }
  }

  /**
   * Sign in admin or department officer (server-side only)
   */
  async signInUser(email: string, password: string, role: 'admin' | 'department_officer') {
    try {
      // Check if user exists with correct role using admin client
      const { data: userData, error: userError } = await supabaseAdmin
        .from('users')
        .select('id, is_active, role')
        .eq('email', email)
        .eq('role', role)
        .single()

      if (userError || !userData) {
        return { user: null, session: null, error: 'Invalid email or password' }
      }

      if (!userData.is_active) {
        return { user: null, session: null, error: 'Account is deactivated' }
      }

      // Sign in with email and password using regular auth client
      const { data, error } = await supabaseAuth.auth.signInWithPassword({
        email,
        password
      })

      if (error || !data.user) {
        return { user: null, session: null, error: error?.message }
      }

      // Update user metadata with role and profile information
      try {
        const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
          data.user.id,
          {
            user_metadata: {
              role: userData.role,
              name: userData.name || userData.email,
              email: userData.email,
              is_active: userData.is_active
            }
          }
        )

        if (updateError) {
          console.error('Error updating user metadata:', updateError)
        }
      } catch (metadataError) {
        console.error('Error setting user metadata:', metadataError)
      }

      return { user: data.user, session: data.session, error: error?.message }
    } catch (error) {
      console.error('Error signing in user:', error)
      return { user: null, session: null, error: 'Sign in failed' }
    }
  }
}

export const serverAuthService = new ServerAuthService()
