'use client'

import { useState, useEffect, createContext, useContext } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { clientAuthService, AuthUser } from '@/lib/auth-client'

interface AuthContextType {
  user: AuthUser | null
  profile: any | null
  loading: boolean
  signIn: (email: string, password: string, role?: 'admin' | 'department_officer') => Promise<any>
  signInStudent: (matricule: string, password: string) => Promise<any>
  signUp: (data: any) => Promise<any>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<any>
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function useAuthProvider() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [profile, setProfile] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session with new approach
    const getInitialSession = async () => {
      console.log('🔍 [useAuth] Getting initial session with new approach...')
      try {
        // Use the new getCurrentUser method that checks localStorage first
        const { user: currentUser, profile: currentProfile } = await clientAuthService.getCurrentUser()
        console.log('🔍 [useAuth] Initial session result:', {
          user: currentUser ? { email: currentUser.email, role: currentUser.role } : null,
          profile: currentProfile
        })

        setUser(currentUser)
        setProfile(currentProfile)
        setLoading(false)
        console.log('🔍 [useAuth] Initial session complete, loading set to false')
      } catch (error) {
        console.error('🔍 [useAuth] Error getting initial session:', error)
        // Don't leave the user hanging - set loading to false even on error
        setUser(null)
        setProfile(null)
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔍 [useAuth] Auth state change:', { event, session: !!session, user: !!session?.user })

        if (event === 'SIGNED_OUT') {
          console.log('🔍 [useAuth] User signed out, clearing data')
          setUser(null)
          setProfile(null)
          setLoading(false)
          return
        }

        if (session?.user) {
          try {
            // Use a timeout to prevent hanging
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Auth state change timeout')), 3000)
            )

            const authPromise = clientAuthService.getCurrentUser()
            const { user: currentUser, profile: currentProfile } = await Promise.race([authPromise, timeoutPromise]) as any

            console.log('🔍 [useAuth] Auth change - got user:', {
              user: currentUser ? { email: currentUser.email, role: currentUser.role } : null
            })
            setUser(currentUser)
            setProfile(currentProfile)
          } catch (error) {
            console.error('🔍 [useAuth] Error in auth state change:', error)
            // Don't clear user data on timeout - keep existing data
            if (error.message !== 'Auth state change timeout') {
              setUser(null)
              setProfile(null)
            }
          }
        } else {
          console.log('🔍 [useAuth] Auth change - no session, clearing user')
          setUser(null)
          setProfile(null)
        }
        setLoading(false)
        console.log('🔍 [useAuth] Auth state change complete, loading set to false')
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string, role: 'admin' | 'department_officer' = 'admin') => {
    setLoading(true)
    try {
      const result = await clientAuthService.signInUser(email, password, role)
      return result
    } finally {
      setLoading(false)
    }
  }

  const signInStudent = async (matricule: string, password: string) => {
    setLoading(true)
    try {
      const result = await clientAuthService.signInStudent(matricule, password)
      return result
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (data: any) => {
    setLoading(true)
    try {
      const result = await clientAuthService.signUpStudent(data)
      return result
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await clientAuthService.signOut()
      setUser(null)
      setProfile(null)
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    return await clientAuthService.resetPassword(email)
  }

  return {
    user,
    profile,
    loading,
    signIn,
    signInStudent,
    signUp,
    signOut,
    resetPassword
  }
}
