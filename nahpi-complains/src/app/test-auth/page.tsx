'use client'

import { useState, useEffect } from 'react'
import { clientAuthService } from '@/lib/auth-client'

export default function TestAuthPage() {
  const [authState, setAuthState] = useState<any>(null)
  const [logs, setLogs] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }

  useEffect(() => {
    addLog('Component mounted, setting up auth listener...')
    
    // Listen for auth changes
    const unsubscribe = clientAuthService.onAuthChange(() => {
      addLog('🔔 Auth change event received!')
      refreshAuthState()
    })

    // Initial auth state
    refreshAuthState()

    return () => {
      addLog('Component unmounting, cleaning up listener...')
      unsubscribe()
    }
  }, [])

  const refreshAuthState = async () => {
    try {
      addLog('🔍 Refreshing auth state...')
      const { user, profile } = await clientAuthService.getCurrentUser()
      setAuthState({ user, profile })
      addLog(`✅ Auth state updated: ${user ? `${user.email} (${user.role})` : 'No user'}`)
    } catch (error) {
      addLog(`❌ Error refreshing auth state: ${error}`)
      setAuthState({ user: null, profile: null })
    }
  }

  const testLogin = async () => {
    setIsLoading(true)
    addLog('🔐 Starting test login...')
    
    try {
      // Step 1: Call login API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          identifier: '<EMAIL>',
          password: 'admin123',
          role: 'admin'
        })
      })

      const data = await response.json()
      
      if (!response.ok) {
        addLog(`❌ Login API failed: ${data.error}`)
        return
      }

      addLog('✅ Login API successful')

      // Step 2: Create auth user (like the login form does)
      const authUser = {
        id: data.user.id,
        email: data.user.email,
        role: data.user.user_metadata?.role || 'admin',
        name: data.user.user_metadata?.name,
        matricule: data.user.user_metadata?.matricule,
        department: data.user.user_metadata?.department
      }

      addLog(`📦 Created auth user: ${authUser.email} (${authUser.role})`)

      // Step 3: Store user data (this should trigger the auth change event)
      addLog('💾 Storing user data...')
      clientAuthService.setUserData(authUser, data.session)
      addLog('✅ User data stored, auth change event should fire')

    } catch (error) {
      addLog(`❌ Login test failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const clearAuth = () => {
    addLog('🧹 Clearing localStorage...')
    localStorage.removeItem('nahpi_auth_data')
    setAuthState({ user: null, profile: null })
    addLog('✅ Auth data cleared')
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication System Test</h1>
        
        {/* Current Auth State */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Auth State</h2>
          <div className="bg-gray-50 p-4 rounded">
            <pre className="text-sm">
              {JSON.stringify(authState, null, 2)}
            </pre>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Controls</h2>
          <div className="space-x-4">
            <button
              onClick={testLogin}
              disabled={isLoading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isLoading ? 'Testing Login...' : 'Test Login'}
            </button>
            <button
              onClick={refreshAuthState}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Refresh Auth State
            </button>
            <button
              onClick={clearAuth}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Clear Auth
            </button>
          </div>
        </div>

        {/* Logs */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Event Logs</h2>
          <div className="bg-black text-green-400 p-4 rounded h-96 overflow-y-auto font-mono text-sm">
            {logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
          <button
            onClick={() => setLogs([])}
            className="mt-2 bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600"
          >
            Clear Logs
          </button>
        </div>
      </div>
    </div>
  )
}
