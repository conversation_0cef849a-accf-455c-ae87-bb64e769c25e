import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { serverAuthService } from '@/lib/auth-server'

// GET /api/admin/stats - Get admin dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const { user } = await serverAuthService.getCurrentUserFromRequest(request)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get complaint statistics
    const { data: complaints, error: complaintsError } = await supabaseAdmin
      .from('complaints')
      .select('status, priority, submitted_at, resolved_at')

    if (complaintsError) {
      console.error('Error fetching complaints:', complaintsError)
      return NextResponse.json({ error: 'Failed to fetch complaint stats' }, { status: 500 })
    }

    // Get user statistics
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('role, is_active, created_at')

    if (usersError) {
      console.error('Error fetching users:', usersError)
      return NextResponse.json({ error: 'Failed to fetch user stats' }, { status: 500 })
    }

    // Get department statistics
    const { data: departments, error: departmentsError } = await supabaseAdmin
      .from('departments')
      .select('id, name, is_active')

    if (departmentsError) {
      console.error('Error fetching departments:', departmentsError)
      return NextResponse.json({ error: 'Failed to fetch department stats' }, { status: 500 })
    }

    // Get department officers count
    const { data: officers, error: officersError } = await supabaseAdmin
      .from('department_officers')
      .select('id, department_id')

    if (officersError) {
      console.error('Error fetching officers:', officersError)
      return NextResponse.json({ error: 'Failed to fetch officer stats' }, { status: 500 })
    }

    // Calculate complaint statistics
    const totalComplaints = complaints.length
    const pendingComplaints = complaints.filter(c => c.status === 'pending').length
    const inProgressComplaints = complaints.filter(c => c.status === 'in_progress').length
    const resolvedComplaints = complaints.filter(c => c.status === 'resolved').length
    const rejectedComplaints = complaints.filter(c => c.status === 'rejected').length

    // Calculate overdue complaints (pending for more than 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const overdueComplaints = complaints.filter(c => 
      c.status === 'pending' && new Date(c.submitted_at) < sevenDaysAgo
    ).length

    // Calculate average resolution time
    const resolvedWithTime = complaints.filter(c => c.status === 'resolved' && c.resolved_at)
    const averageResolutionTime = resolvedWithTime.length > 0 
      ? resolvedWithTime.reduce((acc, c) => {
          const submitted = new Date(c.submitted_at)
          const resolved = new Date(c.resolved_at)
          const days = (resolved.getTime() - submitted.getTime()) / (1000 * 60 * 60 * 24)
          return acc + days
        }, 0) / resolvedWithTime.length
      : 0

    // Calculate user statistics
    const totalUsers = users.length
    const totalStudents = users.filter(u => u.role === 'student').length
    const totalOfficers = users.filter(u => u.role === 'department_officer').length
    const activeUsers = users.filter(u => u.is_active).length

    // Calculate department statistics
    const totalDepartments = departments.filter(d => d.is_active).length

    const stats = {
      totalComplaints,
      pendingComplaints,
      inProgressComplaints,
      resolvedComplaints,
      rejectedComplaints,
      overdueComplaints,
      totalUsers,
      totalStudents,
      totalOfficers,
      totalDepartments,
      activeUsers,
      averageResolutionTime: Math.round(averageResolutionTime * 10) / 10
    }

    return NextResponse.json({ stats })
  } catch (error) {
    console.error('Error in GET /api/admin/stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
