import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

// GET /api/departments - Get all active departments
export async function GET(request: NextRequest) {
  try {
    const { data, error } = await supabaseAdmin
      .from('departments')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) {
      console.error('Error fetching departments:', error)
      return NextResponse.json({ error: 'Failed to fetch departments' }, { status: 500 })
    }

    return NextResponse.json({ departments: data })
  } catch (error) {
    console.error('Error in GET /api/departments:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/departments - Create new department (admin only)
export async function POST(request: NextRequest) {
  try {
    const { user } = await authService.getCurrentUserFromRequest(request)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, code, description, head_of_department } = body

    if (!name || !code) {
      return NextResponse.json({ error: 'Name and code are required' }, { status: 400 })
    }

    const { data, error } = await supabaseAdmin
      .from('departments')
      .insert({
        name,
        code: code.toUpperCase(),
        description,
        head_of_department
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating department:', error)
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json({ error: 'Department code already exists' }, { status: 409 })
      }
      return NextResponse.json({ error: 'Failed to create department' }, { status: 500 })
    }

    return NextResponse.json({ department: data }, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/departments:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
