import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { user_id, code } = body

    if (!user_id || !code) {
      return NextResponse.json({ error: 'Missing user ID or verification code' }, { status: 400 })
    }

    const result = await authService.verifyCode(user_id, code)

    if (result.success) {
      return NextResponse.json({ message: 'Account verified successfully' })
    } else {
      return NextResponse.json({ error: 'Verification failed' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error in POST /api/auth/verify:', error)
    
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
