import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json({ error: 'Missing email or password' }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 })
    }

    // Attempt login as department officer
    const result = await authService.signInUser(email, password, 'department_officer')

    if (result.error) {
      console.error('Department officer login error:', result.error)
      
      // Handle specific errors
      if (result.error.message?.includes('Invalid login credentials')) {
        return NextResponse.json({ error: 'Invalid email or password' }, { status: 401 })
      }
      if (result.error.message?.includes('Email not confirmed')) {
        return NextResponse.json({ error: 'Please verify your email first' }, { status: 401 })
      }
      if (result.error.message?.includes('deactivated')) {
        return NextResponse.json({ error: 'Account is deactivated' }, { status: 403 })
      }
      if (result.error.message?.includes('role')) {
        return NextResponse.json({ error: 'Access denied. Department officer account required.' }, { status: 403 })
      }
      
      return NextResponse.json({ 
        error: result.error.message || 'Login failed' 
      }, { status: 401 })
    }

    return NextResponse.json({
      message: 'Login successful',
      user: result.data?.user,
      session: result.data?.session
    })

  } catch (error) {
    console.error('Error in POST /api/department/auth/login:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
