import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { serverAuthService } from '@/lib/auth-server'

// GET /api/department/stats - Get department officer dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const { user, profile } = await serverAuthService.getCurrentUserWithFullProfile(request)
    if (!user || user.role !== 'department_officer') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!profile?.department_id) {
      return NextResponse.json({ error: 'Department officer profile incomplete' }, { status: 400 })
    }

    // Get department information
    const { data: department, error: deptError } = await supabaseAdmin
      .from('departments')
      .select('id, name, code')
      .eq('id', profile.department_id)
      .single()

    if (deptError) {
      console.error('Error fetching department:', deptError)
      return NextResponse.json({ error: 'Failed to fetch department data' }, { status: 500 })
    }

    // Get all complaints for this department
    const { data: complaints, error: complaintsError } = await supabaseAdmin
      .from('complaints')
      .select('id, status, priority, submitted_at, resolved_at')
      .eq('department_id', profile.department_id)

    if (complaintsError) {
      console.error('Error fetching complaints:', complaintsError)
      return NextResponse.json({ error: 'Failed to fetch complaints data' }, { status: 500 })
    }

    // Calculate statistics
    const totalComplaints = complaints.length
    const pendingComplaints = complaints.filter(c => c.status === 'pending').length
    const inProgressComplaints = complaints.filter(c => c.status === 'in_progress').length
    const resolvedComplaints = complaints.filter(c => c.status === 'resolved').length
    const rejectedComplaints = complaints.filter(c => c.status === 'rejected').length

    // Calculate today's new complaints
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayNewComplaints = complaints.filter(c => 
      new Date(c.submitted_at) >= today
    ).length

    // Calculate overdue complaints (pending for more than 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const overdueComplaints = complaints.filter(c => 
      c.status === 'pending' && new Date(c.submitted_at) < sevenDaysAgo
    ).length

    // Calculate average response time for resolved complaints
    const resolvedWithTime = complaints.filter(c => c.status === 'resolved' && c.resolved_at)
    const averageResponseTime = resolvedWithTime.length > 0 
      ? resolvedWithTime.reduce((acc, c) => {
          const submitted = new Date(c.submitted_at)
          const resolved = new Date(c.resolved_at)
          const days = (resolved.getTime() - submitted.getTime()) / (1000 * 60 * 60 * 24)
          return acc + days
        }, 0) / resolvedWithTime.length
      : 0

    // Calculate resolution rate
    const resolutionRate = totalComplaints > 0 ? (resolvedComplaints / totalComplaints) * 100 : 0

    const stats = {
      department: {
        id: department.id,
        name: department.name,
        code: department.code
      },
      departmentComplaints: totalComplaints,
      pendingComplaints,
      inProgressComplaints,
      resolvedComplaints,
      rejectedComplaints,
      todayNewComplaints,
      overdueComplaints,
      averageResponseTime: Math.round(averageResponseTime * 10) / 10,
      resolutionRate: Math.round(resolutionRate * 10) / 10
    }

    return NextResponse.json({ stats })
  } catch (error) {
    console.error('Error in GET /api/department/stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
