'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { clientAuthService } from '@/lib/auth-client'

export default function AuthStateDebug() {
  const { user, loading: authLoading } = useAuth()
  const [localStorageData, setLocalStorageData] = useState<any>(null)
  const [clientServiceData, setClientServiceData] = useState<any>(null)

  useEffect(() => {
    // Check localStorage directly
    const checkLocalStorage = () => {
      try {
        const stored = localStorage.getItem('nahpi_auth_data')
        if (stored) {
          const parsed = JSON.parse(stored)
          setLocalStorageData(parsed)
        } else {
          setLocalStorageData('No data in localStorage')
        }
      } catch (error) {
        setLocalStorageData(`Error reading localStorage: ${error}`)
      }
    }

    // Check client service
    const checkClientService = async () => {
      try {
        const result = await clientAuthService.getCurrentUser()
        setClientServiceData(result)
      } catch (error) {
        setClientServiceData(`Error from client service: ${error}`)
      }
    }

    checkLocalStorage()
    checkClientService()

    // Refresh every 2 seconds
    const interval = setInterval(() => {
      checkLocalStorage()
      checkClientService()
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication State Debug</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* useAuth Hook */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3 text-blue-600">useAuth Hook</h2>
          <div className="space-y-2 text-sm">
            <div><strong>Loading:</strong> {authLoading ? 'true' : 'false'}</div>
            <div><strong>User:</strong> {user ? 'Present' : 'null'}</div>
            {user && (
              <div className="mt-2 p-2 bg-gray-50 rounded">
                <div><strong>Email:</strong> {user.email}</div>
                <div><strong>Role:</strong> {user.role}</div>
                <div><strong>Name:</strong> {user.name}</div>
                <div><strong>ID:</strong> {user.id}</div>
              </div>
            )}
          </div>
        </div>

        {/* localStorage */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3 text-green-600">localStorage</h2>
          <div className="text-sm">
            {typeof localStorageData === 'string' ? (
              <div className="text-gray-600">{localStorageData}</div>
            ) : localStorageData ? (
              <div className="space-y-2">
                <div><strong>Stored At:</strong> {new Date(localStorageData.storedAt).toLocaleString()}</div>
                <div><strong>Expires At:</strong> {new Date(localStorageData.expiresAt).toLocaleString()}</div>
                <div><strong>User Email:</strong> {localStorageData.user?.email}</div>
                <div><strong>User Role:</strong> {localStorageData.user?.role}</div>
                <div><strong>Session:</strong> {localStorageData.session ? 'Present' : 'Missing'}</div>
              </div>
            ) : (
              <div className="text-gray-600">Loading...</div>
            )}
          </div>
        </div>

        {/* Client Service */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3 text-purple-600">Client Service</h2>
          <div className="text-sm">
            {typeof clientServiceData === 'string' ? (
              <div className="text-red-600">{clientServiceData}</div>
            ) : clientServiceData ? (
              <div className="space-y-2">
                <div><strong>User:</strong> {clientServiceData.user ? 'Present' : 'null'}</div>
                {clientServiceData.user && (
                  <div className="mt-2 p-2 bg-gray-50 rounded">
                    <div><strong>Email:</strong> {clientServiceData.user.email}</div>
                    <div><strong>Role:</strong> {clientServiceData.user.role}</div>
                    <div><strong>Name:</strong> {clientServiceData.user.name}</div>
                  </div>
                )}
                <div><strong>Profile:</strong> {clientServiceData.profile ? 'Present' : 'null'}</div>
              </div>
            ) : (
              <div className="text-gray-600">Loading...</div>
            )}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-6 space-x-4">
        <button
          onClick={() => {
            localStorage.removeItem('nahpi_auth_data')
            window.location.reload()
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Clear localStorage
        </button>
        
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh Page
        </button>
      </div>
    </div>
  )
}
