#!/usr/bin/env node

// Test script to verify the complete authentication flow
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testAdminLogin() {
  console.log('🔍 Testing Admin Login Flow...');
  
  try {
    // Step 1: Login via API
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginResponse.ok) {
      console.error('❌ Admin login failed:', loginData);
      return false;
    }

    console.log('✅ Admin login successful');
    console.log('📋 User data:', {
      id: loginData.user.id,
      email: loginData.user.email,
      role: loginData.user.user_metadata?.role,
      name: loginData.user.user_metadata?.name
    });

    // Step 2: Test API access with token
    const statsResponse = await fetch(`${BASE_URL}/api/admin/stats`, {
      headers: {
        'Authorization': `Bearer ${loginData.session.access_token}`,
        'Content-Type': 'application/json'
      }
    });

    const statsData = await statsResponse.json();
    
    if (!statsResponse.ok) {
      console.error('❌ Admin stats API failed:', statsData);
      return false;
    }

    console.log('✅ Admin stats API successful');
    console.log('📊 Stats data:', statsData);

    return true;

  } catch (error) {
    console.error('❌ Admin login test failed:', error);
    return false;
  }
}

async function testDepartmentLogin() {
  console.log('\n🔍 Testing Department Login Flow...');
  
  try {
    // Step 1: Login via API
    const loginResponse = await fetch(`${BASE_URL}/api/department/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'officer123'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginResponse.ok) {
      console.error('❌ Department login failed:', loginData);
      return false;
    }

    console.log('✅ Department login successful');
    console.log('📋 User data:', {
      id: loginData.user?.id,
      email: loginData.user?.email,
      role: loginData.user?.user_metadata?.role,
      name: loginData.user?.user_metadata?.name
    });

    return true;

  } catch (error) {
    console.error('❌ Department login test failed:', error);
    return false;
  }
}

async function testClientSideFlow() {
  console.log('\n🔍 Testing Client-Side Authentication Flow...');

  try {
    // Step 1: Clear any existing localStorage
    console.log('🧹 Clearing localStorage...');

    // Step 2: Login via API (simulating form submission)
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      })
    });

    const loginData = await loginResponse.json();

    if (!loginResponse.ok) {
      console.error('❌ Login failed:', loginData);
      return false;
    }

    console.log('✅ Login API successful');

    // Step 3: Simulate what the login form does - store data in localStorage
    const authUser = {
      id: loginData.user.id,
      email: loginData.user.email,
      role: loginData.user.user_metadata?.role || 'admin',
      name: loginData.user.user_metadata?.name,
      matricule: loginData.user.user_metadata?.matricule,
      department: loginData.user.user_metadata?.department
    };

    const authData = {
      user: authUser,
      session: loginData.session,
      storedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    };

    console.log('💾 Simulating localStorage storage:', {
      user: authUser.email,
      role: authUser.role,
      hasSession: !!loginData.session
    });

    // Step 4: Test dashboard access
    const dashboardResponse = await fetch(`${BASE_URL}/admin/dashboard`);

    if (dashboardResponse.ok) {
      console.log('✅ Dashboard accessible');
    } else {
      console.log('⚠️  Dashboard returned:', dashboardResponse.status);
    }

    return true;

  } catch (error) {
    console.error('❌ Client-side flow test failed:', error);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Authentication Flow Tests...\n');

  const adminResult = await testAdminLogin();
  const departmentResult = await testDepartmentLogin();
  const clientSideResult = await testClientSideFlow();

  console.log('\n📊 Test Results:');
  console.log(`Admin Login API: ${adminResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Department Login API: ${departmentResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Client-Side Flow: ${clientSideResult ? '✅ PASS' : '❌ FAIL'}`);

  if (adminResult && departmentResult && clientSideResult) {
    console.log('\n🎉 All authentication tests passed!');
  } else {
    console.log('\n⚠️  Some authentication tests failed. Check the logs above.');
  }
}

// Run the tests
runTests().catch(console.error);
