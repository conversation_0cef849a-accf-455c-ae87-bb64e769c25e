<!DOCTYPE html>
<html>
<head>
    <title>localStorage Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>localStorage Authentication Test</h1>
    
    <div class="section">
        <h2>Current localStorage Data</h2>
        <pre id="current-data">Loading...</pre>
        <button onclick="refreshData()">Refresh</button>
        <button onclick="clearData()">Clear</button>
    </div>
    
    <div class="section">
        <h2>Test Login Flow</h2>
        <button onclick="testLogin()">Simulate Admin Login</button>
        <button onclick="testGetUser()">Test getCurrentUser</button>
        <pre id="test-results"></pre>
    </div>

    <script>
        function refreshData() {
            const data = localStorage.getItem('nahpi_auth_data');
            document.getElementById('current-data').textContent = data ? JSON.stringify(JSON.parse(data), null, 2) : 'No data found';
        }
        
        function clearData() {
            localStorage.removeItem('nahpi_auth_data');
            refreshData();
        }
        
        async function testLogin() {
            const results = document.getElementById('test-results');
            results.textContent = 'Testing login flow...\n';
            
            try {
                // Step 1: Login API call
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        identifier: '<EMAIL>',
                        password: 'admin123',
                        role: 'admin'
                    })
                });
                
                const data = await response.json();
                results.textContent += `Login API: ${response.ok ? 'SUCCESS' : 'FAILED'}\n`;
                
                if (response.ok) {
                    // Step 2: Store data like the login form does
                    const authUser = {
                        id: data.user.id,
                        email: data.user.email,
                        role: data.user.user_metadata?.role || 'admin',
                        name: data.user.user_metadata?.name,
                        matricule: data.user.user_metadata?.matricule,
                        department: data.user.user_metadata?.department
                    };

                    const authData = {
                        user: authUser,
                        session: data.session,
                        storedAt: new Date().toISOString(),
                        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                    };

                    localStorage.setItem('nahpi_auth_data', JSON.stringify(authData));
                    results.textContent += `Data stored in localStorage\n`;
                    results.textContent += `User: ${authUser.email} (${authUser.role})\n`;
                    
                    refreshData();
                } else {
                    results.textContent += `Error: ${data.error}\n`;
                }
            } catch (error) {
                results.textContent += `Error: ${error.message}\n`;
            }
        }
        
        async function testGetUser() {
            const results = document.getElementById('test-results');
            results.textContent = 'Testing getCurrentUser...\n';
            
            try {
                // Simulate the getCurrentUser logic
                const storedData = localStorage.getItem('nahpi_auth_data');
                if (!storedData) {
                    results.textContent += 'No stored data found\n';
                    return;
                }
                
                const parsed = JSON.parse(storedData);
                const now = new Date();
                const expiresAt = new Date(parsed.expiresAt);
                
                if (now > expiresAt) {
                    results.textContent += 'Data expired\n';
                    localStorage.removeItem('nahpi_auth_data');
                    return;
                }
                
                results.textContent += `Found valid data:\n`;
                results.textContent += `User: ${parsed.user.email}\n`;
                results.textContent += `Role: ${parsed.user.role}\n`;
                results.textContent += `Expires: ${expiresAt.toLocaleString()}\n`;
                
            } catch (error) {
                results.textContent += `Error: ${error.message}\n`;
            }
        }
        
        // Auto-refresh on load
        refreshData();
        setInterval(refreshData, 2000);
    </script>
</body>
</html>
