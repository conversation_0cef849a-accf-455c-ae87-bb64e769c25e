#!/usr/bin/env node

// Test script to simulate the exact browser authentication flow
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function simulateBrowserLogin() {
  console.log('🔍 Simulating Browser Login Flow...\n');
  
  try {
    // Step 1: Clear localStorage (simulate fresh browser)
    console.log('1. 🧹 Clearing localStorage...');
    
    // Step 2: Login via API (exactly like the form does)
    console.log('2. 🔐 Calling login API...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginResponse.ok) {
      console.error('❌ Login failed:', loginData);
      return false;
    }

    console.log('✅ Login API successful');
    console.log('📋 User metadata:', {
      email: loginData.user.email,
      role: loginData.user.user_metadata?.role,
      name: loginData.user.user_metadata?.name
    });

    // Step 3: Simulate what the login form does - create authUser object
    console.log('3. 📦 Creating authUser object...');
    const authUser = {
      id: loginData.user.id,
      email: loginData.user.email,
      role: loginData.user.user_metadata?.role || 'admin',
      name: loginData.user.user_metadata?.name,
      matricule: loginData.user.user_metadata?.matricule,
      department: loginData.user.user_metadata?.department
    };

    console.log('📋 AuthUser object:', authUser);

    // Step 4: Simulate localStorage storage (like clientAuthService.setUserData does)
    console.log('4. 💾 Simulating localStorage storage...');
    const authData = {
      user: authUser,
      session: {
        access_token: loginData.session.access_token,
        refresh_token: loginData.session.refresh_token,
        expires_at: loginData.session.expires_at * 1000 // Convert to milliseconds
      },
      timestamp: Date.now()
    };

    console.log('📋 Storage data structure:', {
      user: authData.user.email,
      role: authData.user.role,
      sessionExpires: new Date(authData.session.expires_at).toLocaleString(),
      timestamp: new Date(authData.timestamp).toLocaleString()
    });

    // Step 5: Simulate localStorage retrieval (like getCurrentUser does)
    console.log('5. 🔍 Simulating localStorage retrieval...');
    
    // Check expiry (24 hours)
    const STORAGE_EXPIRY = 24 * 60 * 60 * 1000;
    const isExpired = Date.now() - authData.timestamp > STORAGE_EXPIRY;
    const isSessionExpired = Date.now() > authData.session.expires_at;
    
    console.log('📋 Expiry check:', {
      dataAge: Math.round((Date.now() - authData.timestamp) / 1000 / 60) + ' minutes',
      isExpired,
      sessionExpires: new Date(authData.session.expires_at).toLocaleString(),
      isSessionExpired
    });

    if (isExpired || isSessionExpired) {
      console.log('❌ Data would be expired, authentication would fail');
      return false;
    }

    console.log('✅ Data is valid, authentication would succeed');

    // Step 6: Test API access with stored token
    console.log('6. 🔗 Testing API access with stored token...');
    const statsResponse = await fetch(`${BASE_URL}/api/admin/stats`, {
      headers: {
        'Authorization': `Bearer ${authData.session.access_token}`,
        'Content-Type': 'application/json'
      }
    });

    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ API access successful');
      console.log('📊 Stats loaded:', Object.keys(statsData.stats || {}));
    } else {
      console.log('❌ API access failed:', statsResponse.status);
      return false;
    }

    // Step 7: Simulate dashboard loading conditions
    console.log('7. 🎯 Simulating dashboard loading conditions...');
    const user = authData.user;
    const stats = true; // Assume stats loaded successfully
    const loading = false; // Data loading complete
    const authLoading = false; // Auth loading complete

    console.log('📋 Dashboard conditions:', {
      hasUser: !!user,
      userRole: user?.role,
      hasStats: stats,
      loading,
      authLoading
    });

    // Dashboard condition: if (loading || !user || !stats)
    const shouldShowLoading = loading || !user || !stats;
    console.log(`📋 Dashboard would ${shouldShowLoading ? 'SHOW LOADING' : 'SHOW CONTENT'}`);

    return !shouldShowLoading;

  } catch (error) {
    console.error('❌ Browser simulation failed:', error);
    return false;
  }
}

async function runBrowserTest() {
  console.log('🚀 Starting Browser Authentication Simulation...\n');
  
  const result = await simulateBrowserLogin();
  
  console.log('\n📊 Test Result:');
  console.log(`Browser Auth Flow: ${result ? '✅ PASS' : '❌ FAIL'}`);
  
  if (result) {
    console.log('\n🎉 Browser authentication simulation passed!');
    console.log('The issue might be in the React component lifecycle or useAuth hook.');
  } else {
    console.log('\n⚠️  Browser authentication simulation failed.');
    console.log('There is a fundamental issue with the authentication flow.');
  }
}

// Run the test
runBrowserTest().catch(console.error);
