#!/usr/bin/env node

// Final comprehensive test to verify the authentication system works end-to-end
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testCompleteAuthFlow() {
  console.log('🔍 Testing Complete Authentication Flow...\n');
  
  try {
    // Step 1: Test login API
    console.log('1. 🔐 Testing login API...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginResponse.ok) {
      console.error('❌ Login API failed:', loginData);
      return { success: false, step: 'login_api', error: loginData };
    }

    console.log('✅ Login API successful');
    console.log('📋 User data:', {
      email: loginData.user.email,
      role: loginData.user.user_metadata?.role,
      hasSession: !!loginData.session,
      sessionExpires: new Date(loginData.session.expires_at * 1000).toLocaleString()
    });

    // Step 2: Test stats API with token
    console.log('\n2. 📊 Testing stats API with token...');
    const statsResponse = await fetch(`${BASE_URL}/api/admin/stats`, {
      headers: {
        'Authorization': `Bearer ${loginData.session.access_token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statsResponse.ok) {
      console.error('❌ Stats API failed:', statsResponse.status);
      return { success: false, step: 'stats_api', error: `HTTP ${statsResponse.status}` };
    }

    const statsData = await statsResponse.json();
    console.log('✅ Stats API successful');
    console.log('📊 Stats keys:', Object.keys(statsData.stats || {}));

    // Step 3: Test dashboard access
    console.log('\n3. 🎯 Testing dashboard access...');
    const dashboardResponse = await fetch(`${BASE_URL}/admin/dashboard`);
    
    if (!dashboardResponse.ok) {
      console.error('❌ Dashboard access failed:', dashboardResponse.status);
      return { success: false, step: 'dashboard_access', error: `HTTP ${dashboardResponse.status}` };
    }

    console.log('✅ Dashboard accessible');

    // Step 4: Simulate localStorage storage (what the login form does)
    console.log('\n4. 💾 Simulating localStorage storage...');
    const authUser = {
      id: loginData.user.id,
      email: loginData.user.email,
      role: loginData.user.user_metadata?.role || 'admin',
      name: loginData.user.user_metadata?.name,
      matricule: loginData.user.user_metadata?.matricule,
      department: loginData.user.user_metadata?.department
    };

    const authData = {
      user: authUser,
      session: {
        access_token: loginData.session.access_token,
        refresh_token: loginData.session.refresh_token,
        expires_at: loginData.session.expires_at * 1000
      },
      timestamp: Date.now()
    };

    console.log('✅ Auth data structure created');
    console.log('📋 Storage simulation:', {
      user: authData.user.email,
      role: authData.user.role,
      sessionValid: Date.now() < authData.session.expires_at,
      dataAge: '0 seconds'
    });

    // Step 5: Simulate getCurrentUser logic
    console.log('\n5. 🔍 Simulating getCurrentUser logic...');
    
    // Check if data would be valid
    const STORAGE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours
    const isDataExpired = Date.now() - authData.timestamp > STORAGE_EXPIRY;
    const isSessionExpired = Date.now() > authData.session.expires_at;
    
    if (isDataExpired || isSessionExpired) {
      console.error('❌ Stored data would be expired');
      return { success: false, step: 'data_expiry', error: 'Data expired' };
    }

    console.log('✅ Stored data would be valid');
    console.log('📋 getCurrentUser would return:', {
      user: authData.user.email,
      role: authData.user.role,
      loading: false
    });

    // Step 6: Simulate dashboard loading conditions
    console.log('\n6. 🎯 Simulating dashboard loading conditions...');
    
    const user = authData.user;
    const stats = true; // Stats API worked
    const loading = false; // Auth complete
    const authLoading = false; // Auth complete

    console.log('📋 Dashboard state simulation:', {
      hasUser: !!user,
      userRole: user?.role,
      hasStats: stats,
      loading,
      authLoading
    });

    // Dashboard condition: if (loading || !user || !stats)
    const shouldShowLoading = loading || !user || !stats;
    console.log(`📋 Dashboard would: ${shouldShowLoading ? '🔄 SHOW LOADING' : '✅ SHOW CONTENT'}`);

    // Step 7: Test complete flow success
    console.log('\n7. 🎉 Testing complete flow...');
    
    const flowSuccess = !shouldShowLoading && 
                       !!user && 
                       user.role === 'admin' && 
                       stats;

    if (flowSuccess) {
      console.log('✅ Complete authentication flow would work!');
      return { 
        success: true, 
        user: user.email, 
        role: user.role,
        dashboardReady: true
      };
    } else {
      console.error('❌ Authentication flow has issues');
      return { 
        success: false, 
        step: 'flow_validation',
        error: 'Flow validation failed',
        details: { shouldShowLoading, user: !!user, stats }
      };
    }

  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return { success: false, step: 'exception', error: error.message };
  }
}

async function runFinalTest() {
  console.log('🚀 Starting Final Authentication System Test...\n');
  
  const result = await testCompleteAuthFlow();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL TEST RESULTS');
  console.log('='.repeat(60));
  
  if (result.success) {
    console.log('🎉 AUTHENTICATION SYSTEM: ✅ WORKING');
    console.log(`👤 User: ${result.user}`);
    console.log(`🔑 Role: ${result.role}`);
    console.log(`📊 Dashboard: ${result.dashboardReady ? 'Ready' : 'Not Ready'}`);
    console.log('\n✨ The authentication system should work correctly in the browser!');
  } else {
    console.log('❌ AUTHENTICATION SYSTEM: 🚫 FAILED');
    console.log(`🔍 Failed at step: ${result.step}`);
    console.log(`❗ Error: ${result.error}`);
    if (result.details) {
      console.log('📋 Details:', result.details);
    }
    console.log('\n⚠️  The authentication system needs additional fixes.');
  }
  
  console.log('='.repeat(60));
}

// Run the final test
runFinalTest().catch(console.error);
