-- <PERSON><PERSON><PERSON><PERSON> Complaints - Test Users Seeding Script
-- This script creates default test users for development and testing
-- Run this in your Supabase SQL Editor after setting up the main schema

-- Note: This script assumes you have already run the main schema (supabase-schema.sql)
-- and that the default departments have been created

-- First, let's get the Computer Science department ID for our test department officer
-- We'll use this in the department officer creation

-- Create test users in auth.users table first
-- Note: In a real scenario, these would be created through the Supabase Auth API
-- For testing purposes, we'll insert directly (this requires admin access)

-- Test Student User
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    crypt('password123', gen_salt('bf')), -- Password: password123
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
) ON CONFLICT (id) DO NOTHING;

-- Test Admin User
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '22222222-2222-2222-2222-222222222222',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    crypt('admin123', gen_salt('bf')), -- Password: admin123
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
) ON CONFLICT (id) DO NOTHING;

-- Test Department Officer User
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '33333333-3333-3333-3333-333333333333',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    crypt('officer123', gen_salt('bf')), -- Password: officer123
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
) ON CONFLICT (id) DO NOTHING;

-- Now create the corresponding records in our public.users table

-- Test Student in public.users
INSERT INTO public.users (
    id,
    email,
    name,
    role,
    is_active
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    '<EMAIL>',
    'Test Student',
    'student',
    true
) ON CONFLICT (id) DO NOTHING;

-- Test Admin in public.users
INSERT INTO public.users (
    id,
    email,
    name,
    role,
    is_active
) VALUES (
    '22222222-2222-2222-2222-222222222222',
    '<EMAIL>',
    'Test Administrator',
    'admin',
    true
) ON CONFLICT (id) DO NOTHING;

-- Test Department Officer in public.users
INSERT INTO public.users (
    id,
    email,
    name,
    role,
    is_active
) VALUES (
    '33333333-3333-3333-3333-333333333333',
    '<EMAIL>',
    'Dr. Test Officer',
    'department_officer',
    true
) ON CONFLICT (id) DO NOTHING;

-- Create student profile with new matricule format (UBa25E1000)
INSERT INTO public.students (
    id,
    user_id,
    matricule,
    department,
    year_of_study,
    phone_number,
    academic_year,
    verification_method,
    is_verified
) VALUES (
    '44444444-4444-4444-4444-444444444444',
    '11111111-1111-1111-1111-111111111111',
    'UBa25E1000',
    'Computer Science',
    3,
    '+237123456789',
    '2024-2025',
    'email',
    true
) ON CONFLICT (matricule) DO NOTHING;

-- Create department officer profile
-- First, get the Computer Science department ID
INSERT INTO public.department_officers (
    id,
    user_id,
    department_id,
    position
) VALUES (
    '55555555-5555-5555-5555-555555555555',
    '33333333-3333-3333-3333-333333333333',
    (SELECT id FROM public.departments WHERE code = 'CS' LIMIT 1),
    'Assistant Professor'
) ON CONFLICT (user_id) DO NOTHING;

-- Create a sample complaint for testing
INSERT INTO public.complaints (
    id,
    complaint_id,
    student_id,
    title,
    description,
    category,
    status,
    priority,
    course_code,
    course_title,
    course_level,
    semester,
    academic_year,
    department_id,
    assigned_to
) VALUES (
    '66666666-6666-6666-6666-666666666666',
    'CMP-2025-001',
    '44444444-4444-4444-4444-444444444444',
    'CA Mark Discrepancy in Data Structures',
    'I believe there is an error in my CA mark calculation for the Data Structures course. My expected score based on assignments and tests should be higher than what was recorded.',
    'ca_mark',
    'pending',
    'medium',
    'CS301',
    'Data Structures and Algorithms',
    'Level 3',
    'Semester 1',
    '2024-2025',
    (SELECT id FROM public.departments WHERE code = 'CS' LIMIT 1),
    '55555555-5555-5555-5555-555555555555'
) ON CONFLICT (complaint_id) DO NOTHING;

-- Create a notification for the department officer about the new complaint
INSERT INTO public.notifications (
    id,
    user_id,
    type,
    title,
    message,
    is_read,
    related_complaint_id
) VALUES (
    '77777777-7777-7777-7777-777777777777',
    '33333333-3333-3333-3333-333333333333',
    'complaint_assigned',
    'New Complaint Assigned',
    'A new complaint "CA Mark Discrepancy in Data Structures" has been assigned to you.',
    false,
    '66666666-6666-6666-6666-666666666666'
) ON CONFLICT (id) DO NOTHING;

-- Display the created test users
SELECT 
    'Test users created successfully!' as message,
    'Student: <EMAIL> (password: password123, matricule: UBa25E1000)' as student_login,
    'Admin: <EMAIL> (password: admin123)' as admin_login,
    'Department Officer: <EMAIL> (password: officer123)' as officer_login;
