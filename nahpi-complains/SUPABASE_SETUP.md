# NAHPi Complaints - Supabase Backend Setup Guide

## 🚀 Quick Start

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create an account
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: NAHPi Complaints
   - **Database Password**: Choose a strong password
   - **Region**: Select closest to your location
5. Wait for project to be created (2-3 minutes)

### 2. Get Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)
   - **Service role key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 3. Configure Environment Variables

1. Open the `.env.local` file in your project root
2. Replace the placeholder values with your actual Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=NAHPi Complaints

# Email Configuration (optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# SMS Configuration (optional - for verification)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

### 4. Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Click **New Query**
3. Copy the entire content from `supabase-schema.sql` file
4. Paste it into the SQL editor
5. Click **Run** to execute the schema creation

### 5. Set Up Row Level Security (RLS)

1. In the SQL Editor, create another new query
2. Copy the entire content from `supabase-rls-policies.sql` file
3. Paste it into the SQL editor
4. Click **Run** to execute the RLS policies

### 6. Configure Authentication

1. Go to **Authentication** → **Settings** in your Supabase dashboard
2. Under **Auth Settings**:
   - Enable **Email confirmations** if you want email verification
   - Set **Site URL** to `http://localhost:3000` (for development)
   - Add `http://localhost:3000/**` to **Redirect URLs**

## 📊 Database Schema Overview

### Core Tables Created:

- **users** - Base user information (extends Supabase auth.users)
- **students** - Student-specific data (matricule, department, etc.)
- **departments** - Academic departments
- **department_officers** - Department staff information
- **complaints** - Main complaints table
- **complaint_attachments** - File attachments for complaints
- **complaint_responses** - Communication between users
- **complaint_feedback** - Student feedback on resolved complaints
- **notifications** - System notifications
- **verification_codes** - Email/SMS verification codes
- **system_settings** - Application configuration

### Default Data Inserted:

- 8 academic departments (CS, Math, Physics, etc.)
- System settings for complaint deadlines and file uploads

## 🔐 Authentication Flow

### Student Registration:

1. Student fills registration form
2. Account created in Supabase Auth
3. User profile created in `users` table
4. Student profile created in `students` table
5. Verification code sent via email/SMS
6. Student verifies account to activate

### Login Methods:

- **Students**: Login with matricule + password
- **Admins**: Login with email + password
- **Department Officers**: Login with email + password

## 🛡️ Security Features

### Row Level Security (RLS):

- Students can only see their own complaints
- Department officers can only see complaints assigned to their department
- Admins can see all data
- Proper access control for all operations

### Data Validation:

- Email format validation
- Matricule format validation (UBa25E1000)
- Phone number validation
- Password strength requirements

## 🔧 API Endpoints Created

### Authentication:

- `POST /api/auth/register` - Student registration
- `POST /api/auth/login` - User login (all roles)
- `POST /api/auth/verify` - Account verification

### Complaints:

- `GET /api/complaints` - List complaints (filtered by user role)
- `POST /api/complaints` - Create new complaint
- `GET /api/complaints/[id]` - Get specific complaint
- `PATCH /api/complaints/[id]` - Update complaint status
- `DELETE /api/complaints/[id]` - Delete complaint (admin only)

### Departments:

- `GET /api/departments` - List all active departments
- `POST /api/departments` - Create department (admin only)

## 🧪 Testing the Setup

### 1. Start the Development Server

```bash
npm run dev
```

### 2. Test Database Connection

- Visit `http://localhost:3000`
- Try registering a new student account
- Check if data appears in your Supabase dashboard

### 3. Verify Authentication

- Complete student registration
- Try logging in with matricule and password
- Check if session is maintained across page refreshes

## 📝 Next Steps

1. **Complete the remaining tasks** in the task list
2. **Implement file upload** for complaint attachments
3. **Set up email/SMS services** for notifications
4. **Add admin dashboard** for user management
5. **Implement department officer features**
6. **Add reporting and analytics**

## 🐛 Troubleshooting

### Common Issues:

1. **"Invalid API key"** - Check your environment variables
2. **"Row Level Security policy violation"** - Ensure RLS policies are applied
3. **"Duplicate key error"** - Email or matricule already exists
4. **"Connection refused"** - Check if Supabase project is active

### Debug Tips:

- Check browser console for errors
- Monitor Supabase logs in the dashboard
- Verify environment variables are loaded correctly
- Ensure database schema is properly created

## 📞 Support

If you encounter any issues:

1. Check the Supabase documentation
2. Review the error logs in your Supabase dashboard
3. Verify all environment variables are correctly set
4. Ensure the database schema was created successfully
