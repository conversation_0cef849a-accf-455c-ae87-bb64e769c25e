I want to build a Use this color #08387F and white then you can add any other color which will blend (but very very minimal use). Make sure you make the interfaces to be responsive (So that the website will look good on all like screens like desktop and mobile apps). First of all, Let's do the frontend with reactjs and tailwind css. Make sure each user has their own login interface and also make sure you implement the front end based on the user and system requirements in the document 'Technical & Tasks.txt'. You are free to give suggestions on how to improve the requirements and also how to implement the system. Make sure the front end is completely functional then we can move to the backend